import { Link } from "react-router"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuList,
} from "@/components/ui/navigation-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const Navbar = () => {
  return (
    <header className="flex items-center justify-between px-6 py-4 border-b shadow-sm">
      {/* Logo */}
      <Link to="/" className="text-xl font-bold">
        MyBrand
      </Link>

      {/* Links */}
      <NavigationMenu>
        <NavigationMenuList className="hidden md:flex gap-4">
          <NavigationMenuItem>
            <Link to="/about" className="hover:underline">
              About
            </Link>
          </NavigationMenuItem>
          <NavigationMenuItem>
            <Link to="/services" className="hover:underline">
              Services
            </Link>
          </NavigationMenuItem>
          <NavigationMenuItem>
            <Link to="/contact" className="hover:underline">
              Contact
            </Link>
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>

      {/* Right Side */}
      <div className="flex items-center gap-4">
        <Button variant="outline">Sign In</Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Avatar className="cursor-pointer">
              <AvatarImage src="/avatar.png" alt="user" />
              <AvatarFallback>U</AvatarFallback>
            </Avatar>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>Profile</DropdownMenuItem>
            <DropdownMenuItem>Settings</DropdownMenuItem>
            <DropdownMenuItem>Logout</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}

export default Navbar